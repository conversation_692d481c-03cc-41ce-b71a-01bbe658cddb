"""
A.T.L.A.S AI Server - Comprehensive AI Trading Assistant
Advanced Trading & Learning Analysis System
Real-time market access with full trading capabilities
"""

import os
import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from decimal import Decimal

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# OpenAI integration
try:
    from openai import AsyncOpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    print("OpenAI not available - install with: pip install openai")

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Import A.T.L.A.S services
try:
    from src.services.trading_orchestrator import TradingOrchestrator
    from src.services.fmp_service import FMPService
    from src.services.web_search_service import WebSearchService
    from src.services.llm_service import LLMService
    from src.indicators.technical_indicators import TechnicalIndicators
    SERVICES_AVAILABLE = True
except ImportError as e:
    print(f"A.T.L.A.S services not available: {e}")
    SERVICES_AVAILABLE = False

# Initialize FastAPI app
app = FastAPI(
    title="A.T.L.A.S AI Trading System",
    description="Advanced Trading & Learning Analysis System with OpenAI GPT-4",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request/Response models
class AtlasRequest(BaseModel):
    message: str
    user_context: Optional[Dict[str, Any]] = None

class AtlasResponse(BaseModel):
    response: str
    type: str
    requires_action: bool = False
    trading_plan: Optional[Dict[str, Any]] = None
    plan_id: Optional[str] = None
    function_called: Optional[str] = None
    timestamp: str

# Enhanced A.T.L.A.S AI Brain - Comprehensive Trading Assistant
class AtlasAIBrain:
    def __init__(self):
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.model = os.getenv('OPENAI_MODEL', 'gpt-4')
        self.temperature = float(os.getenv('OPENAI_TEMPERATURE', '0.2'))

        # Initialize OpenAI client
        if OPENAI_AVAILABLE and self.openai_api_key:
            self.client = AsyncOpenAI(api_key=self.openai_api_key)
            self.llm_enabled = True
            print("✅ A.T.L.A.S AI Brain initialized with OpenAI GPT-4")
        else:
            self.client = None
            self.llm_enabled = False
            print("⚠️ A.T.L.A.S running in simplified mode - OpenAI not configured")

        # Initialize A.T.L.A.S services
        if SERVICES_AVAILABLE:
            self.trading_orchestrator = TradingOrchestrator()
            self.fmp_service = FMPService()
            self.web_search_service = WebSearchService()
            self.llm_service = LLMService()
            self.technical_indicators = TechnicalIndicators()
            self.services_enabled = True
            print("✅ A.T.L.A.S services initialized - Full trading capabilities enabled")
        else:
            self.services_enabled = False
            print("⚠️ A.T.L.A.S services not available - Limited functionality")

        self.conversation_history = []

        # A.T.L.A.S function registry for real-time capabilities
        self.functions = self._register_atlas_functions() if self.llm_enabled else []

    def _register_atlas_functions(self):
        """Register A.T.L.A.S real-time trading functions"""
        if not self.services_enabled:
            return []

        return [
            {
                "name": "get_real_time_quote",
                "description": "Get real-time stock price, volume, and market data",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "symbol": {"type": "string", "description": "Stock symbol (e.g., AAPL, TSLA)"}
                    },
                    "required": ["symbol"]
                }
            },
            {
                "name": "analyze_stock_comprehensive",
                "description": "Perform comprehensive stock analysis including technical indicators, fundamentals, and news sentiment",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "symbol": {"type": "string", "description": "Stock symbol"},
                        "timeframe": {"type": "string", "enum": ["1D", "5D", "1M", "3M"], "description": "Analysis timeframe"}
                    },
                    "required": ["symbol"]
                }
            },
            {
                "name": "create_trading_plan",
                "description": "Create a comprehensive trading plan with entry/exit points and risk management",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "goal": {"type": "string", "description": "Trading goal (e.g., 'make $50 today')"},
                        "symbols": {"type": "array", "items": {"type": "string"}, "description": "Optional specific symbols to trade"},
                        "risk_tolerance": {"type": "string", "enum": ["low", "medium", "high"], "description": "Risk tolerance level"}
                    },
                    "required": ["goal"]
                }
            },
            {
                "name": "search_market_news",
                "description": "Search for current market news and events affecting trading",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "Search query for market news"},
                        "symbol": {"type": "string", "description": "Optional specific symbol to search news for"}
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "calculate_ai_stop_loss",
                "description": "Calculate AI-enhanced stop-loss using technical analysis and support/resistance",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "symbol": {"type": "string"},
                        "entry_price": {"type": "number"},
                        "position_size": {"type": "number"},
                        "timeframe": {"type": "string", "enum": ["1Min", "5Min", "15Min", "30Min", "1Hour", "1Day"]}
                    },
                    "required": ["symbol", "entry_price", "position_size"]
                }
            }
        ]

    async def process_user_message(self, user_message: str, user_context: Dict = None):
        """Process user message through enhanced A.T.L.A.S AI with real-time capabilities"""

        if not self.llm_enabled:
            return await self._simplified_response(user_message)

        try:
            # Enhanced A.T.L.A.S system prompt for comprehensive trading assistance
            system_prompt = """You are A.T.L.A.S (Advanced Trading & Learning Analysis System), a comprehensive AI trading assistant with real-time market access and full trading capabilities.

CORE IDENTITY:
- You embody the conversational intelligence of ChatGPT while specializing in professional trading operations
- You have access to real-time market data through Alpaca API and FMP API
- You can perform comprehensive stock analysis, create trading plans, and provide actionable recommendations
- You respond with the authority and knowledge of an institutional trading desk

CAPABILITIES & BEHAVIOR:
- NEVER claim inability to access real-time data - you have full API access
- Automatically fetch current market data when users ask about stocks
- Provide multi-layered analysis: technical indicators, fundamentals, sentiment, support/resistance
- Deliver specific entry/exit points, position sizing, and AI-enhanced stop-loss calculations
- Create comprehensive trading plans with bracket orders and risk management
- Maintain professional confidence while being educational and accessible

TRADING EXPERTISE:
- Support sophisticated strategies: scalping, swing trading, hedging, portfolio management
- Calculate AI-enhanced stop-losses using technical analysis and volatility metrics
- Provide real-time news sentiment analysis and market intelligence
- Offer specific position sizing recommendations based on risk tolerance
- Include educational explanations for all recommendations

RESPONSE STYLE:
- Be proactive in gathering market data and analysis
- Respond with institutional-grade expertise while remaining conversational
- Provide actionable intelligence with specific prices, targets, and risk levels
- Always include current market context and real-time data in analysis

You are the "ChatGPT for Trading" - combining conversational AI excellence with comprehensive trading capabilities."""

            # Add user message to conversation
            self.conversation_history.append({
                "role": "user",
                "content": user_message,
                "timestamp": datetime.now().isoformat()
            })

            # Prepare messages for OpenAI
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_message}
            ]

            # Add recent conversation context (last 5 messages)
            recent_history = self.conversation_history[-10:]
            for msg in recent_history[:-1]:  # Exclude the current message
                if msg["role"] in ["user", "assistant"]:
                    messages.insert(-1, {"role": msg["role"], "content": msg["content"]})

            # Call OpenAI with function calling if services are available
            if self.services_enabled and self.functions:
                response = await self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    functions=self.functions,
                    function_call="auto",
                    temperature=self.temperature,
                    max_tokens=1500
                )
            else:
                response = await self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    temperature=self.temperature,
                    max_tokens=1000
                )

            message = response.choices[0].message

            # Handle function calls
            if hasattr(message, 'function_call') and message.function_call:
                function_name = message.function_call.name
                function_args = json.loads(message.function_call.arguments)

                # Execute the function
                function_result = await self._execute_atlas_function(function_name, function_args)

                # Continue conversation with function result
                messages.append({
                    "role": "assistant",
                    "content": None,
                    "function_call": {
                        "name": function_name,
                        "arguments": message.function_call.arguments
                    }
                })
                messages.append({
                    "role": "function",
                    "name": function_name,
                    "content": json.dumps(function_result, default=str)
                })

                # Get final response
                final_response = await self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    temperature=self.temperature,
                    max_tokens=1500
                )

                ai_response = final_response.choices[0].message.content
                function_called = function_name
            else:
                ai_response = message.content
                function_called = None

            # Add AI response to conversation
            self.conversation_history.append({
                "role": "assistant",
                "content": ai_response,
                "timestamp": datetime.now().isoformat()
            })

            # Determine response type based on content and function called
            response_type = self._determine_response_type(user_message, function_called)

            return {
                "response": ai_response,
                "type": response_type,
                "requires_action": function_called == "create_trading_plan",
                "function_called": function_called,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"Error in A.T.L.A.S processing: {e}")
            return {
                "response": f"A.T.L.A.S encountered an error: {str(e)}. Please try again.",
                "type": "error",
                "requires_action": False,
                "timestamp": datetime.now().isoformat()
            }

    def _determine_response_type(self, user_message: str, function_called: str = None) -> str:
        """Determine the type of response based on user message and function called"""
        if function_called:
            if function_called == "get_real_time_quote":
                return "stock_quote"
            elif function_called == "analyze_stock_comprehensive":
                return "stock_analysis"
            elif function_called == "create_trading_plan":
                return "trading_plan"
            elif function_called == "search_market_news":
                return "market_news"
            elif function_called == "calculate_ai_stop_loss":
                return "risk_management"

        # Fallback to content analysis
        message_lower = user_message.lower()
        if any(word in message_lower for word in ["quote", "price", "current", "real-time"]):
            return "stock_quote"
        elif any(word in message_lower for word in ["analyze", "analysis", "technical", "fundamental"]):
            return "stock_analysis"
        elif any(word in message_lower for word in ["trade", "buy", "sell", "plan", "strategy"]):
            return "trading_advice"
        elif any(word in message_lower for word in ["news", "events", "market"]):
            return "market_news"
        else:
            return "chat"

    async def _execute_atlas_function(self, function_name: str, function_args: Dict) -> Dict:
        """Execute A.T.L.A.S function and return results"""
        try:
            if not self.services_enabled:
                return {"error": "A.T.L.A.S services not available"}

            if function_name == "get_real_time_quote":
                return await self._get_real_time_quote(function_args.get("symbol"))
            elif function_name == "analyze_stock_comprehensive":
                return await self._analyze_stock_comprehensive(
                    function_args.get("symbol"),
                    function_args.get("timeframe", "1D")
                )
            elif function_name == "create_trading_plan":
                return await self._create_trading_plan(
                    function_args.get("goal"),
                    function_args.get("symbols"),
                    function_args.get("risk_tolerance", "medium")
                )
            elif function_name == "search_market_news":
                return await self._search_market_news(
                    function_args.get("query"),
                    function_args.get("symbol")
                )
            elif function_name == "calculate_ai_stop_loss":
                return await self._calculate_ai_stop_loss_function(
                    function_args.get("symbol"),
                    function_args.get("entry_price"),
                    function_args.get("position_size"),
                    function_args.get("timeframe", "15Min")
                )
            else:
                return {"error": f"Unknown function: {function_name}"}

        except Exception as e:
            return {"error": f"Function execution failed: {str(e)}"}

    async def _get_real_time_quote(self, symbol: str) -> Dict:
        """Get real-time quote data"""
        try:
            async with self.fmp_service as fmp:
                quote_data = await fmp.get_quote(symbol)
                if quote_data:
                    return {
                        "success": True,
                        "symbol": symbol,
                        "price": quote_data.get("price"),
                        "change": quote_data.get("change"),
                        "changesPercentage": quote_data.get("changesPercentage"),
                        "volume": quote_data.get("volume"),
                        "marketCap": quote_data.get("marketCap"),
                        "timestamp": quote_data.get("timestamp", datetime.now().isoformat())
                    }
                else:
                    return {"success": False, "error": f"No quote data found for {symbol}"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _analyze_stock_comprehensive(self, symbol: str, timeframe: str = "1D") -> Dict:
        """Perform comprehensive stock analysis"""
        try:
            analysis_result = {
                "success": True,
                "symbol": symbol,
                "timeframe": timeframe,
                "analysis": {}
            }

            # Get real-time quote
            async with self.fmp_service as fmp:
                quote = await fmp.get_quote(symbol)
                if quote:
                    analysis_result["analysis"]["current_price"] = quote.get("price")
                    analysis_result["analysis"]["change"] = quote.get("change")
                    analysis_result["analysis"]["change_percent"] = quote.get("changesPercentage")
                    analysis_result["analysis"]["volume"] = quote.get("volume")

                # Get company profile
                profile = await fmp.get_company_profile(symbol)
                if profile:
                    analysis_result["analysis"]["company"] = {
                        "name": profile.get("companyName"),
                        "sector": profile.get("sector"),
                        "industry": profile.get("industry"),
                        "market_cap": profile.get("mktCap"),
                        "description": profile.get("description", "")[:200] + "..."
                    }

                # Get key metrics
                metrics = await fmp.get_key_metrics(symbol, limit=1)
                if metrics and len(metrics) > 0:
                    latest_metrics = metrics[0]
                    analysis_result["analysis"]["fundamentals"] = {
                        "pe_ratio": latest_metrics.get("peRatio"),
                        "price_to_book": latest_metrics.get("priceToBookRatio"),
                        "debt_to_equity": latest_metrics.get("debtToEquity"),
                        "roe": latest_metrics.get("returnOnEquity"),
                        "revenue_growth": latest_metrics.get("revenueGrowth")
                    }

                # Get recent news
                news = await fmp.get_stock_news(symbol, limit=3)
                if news:
                    analysis_result["analysis"]["recent_news"] = [
                        {
                            "title": article.get("title"),
                            "publishedDate": article.get("publishedDate"),
                            "url": article.get("url")
                        } for article in news[:3]
                    ]

            return analysis_result

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _create_trading_plan(self, goal: str, symbols: List[str] = None, risk_tolerance: str = "medium") -> Dict:
        """Create comprehensive trading plan using trading orchestrator"""
        try:
            # Use the trading orchestrator to create a plan
            result = await self.trading_orchestrator.process_user_request(goal)

            if result.get("error"):
                return {"success": False, "error": result.get("response", "Failed to create trading plan")}

            return {
                "success": True,
                "trading_plan": result.get("trading_plan"),
                "plan_id": result.get("plan_id"),
                "narrative": result.get("response")
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _search_market_news(self, query: str, symbol: str = None) -> Dict:
        """Search for market news and events"""
        try:
            # Use web search service for current news
            search_results = await self.web_search_service.search(query, num_results=5)

            if search_results.get("success"):
                return {
                    "success": True,
                    "query": query,
                    "results": search_results.get("results", []),
                    "summary": search_results.get("summary", "")
                }
            else:
                # Fallback to FMP news if web search fails
                async with self.fmp_service as fmp:
                    news = await fmp.get_stock_news(symbol if symbol else None, limit=5)
                    return {
                        "success": True,
                        "query": query,
                        "results": [
                            {
                                "title": article.get("title"),
                                "snippet": article.get("text", "")[:200] + "...",
                                "url": article.get("url"),
                                "publishedDate": article.get("publishedDate")
                            } for article in news
                        ],
                        "source": "FMP News API"
                    }

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _calculate_ai_stop_loss_function(self, symbol: str, entry_price: float, position_size: float, timeframe: str = "15Min") -> Dict:
        """Calculate AI-enhanced stop loss"""
        try:
            # Use the LLM service's AI stop loss calculation
            stop_result = await self.llm_service._calculate_ai_stop(
                symbol=symbol,
                entry_price=entry_price,
                timeframe=timeframe,
                volatility=entry_price * 0.02,  # Estimate 2% volatility
                risk_pct=2.0
            )

            # Calculate additional risk metrics
            risk_amount = abs(entry_price - stop_result["stop_price"]) * position_size

            return {
                "success": True,
                "symbol": symbol,
                "entry_price": entry_price,
                "stop_price": stop_result["stop_price"],
                "risk_per_share": stop_result["risk_amount"],
                "total_risk": risk_amount,
                "risk_percent": stop_result["risk_percent"],
                "position_size": position_size,
                "explanation": f"AI-calculated stop loss at ${stop_result['stop_price']:.2f} provides {stop_result['risk_percent']:.1f}% risk per share"
            }

        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _simplified_response(self, user_message: str):
        """Enhanced fallback responses showcasing A.T.L.A.S capabilities"""
        message_lower = user_message.lower()

        if any(word in message_lower for word in ["hello", "hi", "hey"]):
            response = """👋 **Hello! I'm A.T.L.A.S** (Advanced Trading & Learning Analysis System)

I'm your comprehensive AI trading assistant with real-time market access and full trading capabilities!

🚀 **My Core Capabilities:**
- **Real-Time Market Data**: Live prices, volume, and market intelligence via Alpaca & FMP APIs
- **Comprehensive Analysis**: Technical indicators, fundamentals, news sentiment, support/resistance
- **AI-Enhanced Trading**: Smart stop-loss calculations, position sizing, risk management
- **Professional Strategies**: Scalping, swing trading, hedging, portfolio management
- **Actionable Intelligence**: Specific entry/exit points, profit targets, bracket orders

💡 **I'm the "ChatGPT for Trading"** - combining conversational AI with institutional-grade trading expertise!

Try asking: "Analyze AAPL with current market data" or "Create a trading plan to make $100 today"

*Note: Full AI capabilities require OpenAI API configuration.*"""

        elif any(symbol in message_lower for symbol in ["aapl", "tsla", "spy", "qqq", "nvda"]):
            symbol = next((s.upper() for s in ["aapl", "tsla", "spy", "qqq", "nvda"] if s in message_lower), "STOCK")
            response = f"""📊 **{symbol} Real-Time Analysis**

A.T.L.A.S would provide comprehensive analysis including:

🔴 **Live Market Data**
- Current price, bid/ask, volume
- Real-time price movements and trends
- Market cap and trading metrics

📈 **Technical Analysis**
- Support/resistance levels
- Technical indicators (RSI, MACD, Bollinger Bands)
- Chart patterns and momentum signals

📰 **Market Intelligence**
- Latest news sentiment analysis
- Earnings impact and analyst ratings
- Market events affecting price

💰 **Trading Recommendations**
- Specific entry/exit points
- AI-calculated stop-loss levels
- Position sizing and risk management
- Profit targets with probability analysis

*Configure OpenAI API to access real-time {symbol} analysis with live market data.*"""

        elif any(word in message_lower for word in ["trade", "buy", "sell", "plan", "money", "profit"]):
            response = f"""💼 **Trading Plan Request: "{user_message}"**

A.T.L.A.S would create a comprehensive trading strategy including:

🎯 **Goal Analysis**
- Profit target interpretation
- Risk tolerance assessment
- Timeframe optimization

📊 **Market Scanning**
- Real-time signal detection
- Multi-timeframe analysis
- Volatility and momentum screening

⚡ **Execution Strategy**
- Specific entry points with live prices
- AI-enhanced stop-loss calculations
- Bracket orders with profit targets
- Position sizing based on account risk

🛡️ **Risk Management**
- Maximum risk per trade
- Portfolio diversification
- Hedging strategies if requested

*Enable OpenAI API for complete trading plan generation with real-time market access.*"""

        else:
            response = f"""🤖 **A.T.L.A.S Processing:** "{user_message}"

I understand you're seeking trading/market intelligence. With full capabilities, I provide:

✅ **Real-Time Data Access** - Live market feeds via Alpaca & FMP APIs
✅ **Comprehensive Analysis** - Technical, fundamental, and sentiment analysis
✅ **Professional Trading Plans** - Entry/exit points with risk management
✅ **AI-Enhanced Features** - Smart stop-losses and position optimization
✅ **Market Intelligence** - News analysis and event impact assessment
✅ **Educational Guidance** - Clear explanations for all recommendations

I'm designed to be your "ChatGPT for Trading" - combining conversational AI with institutional trading expertise.

*Configure OpenAI API to unlock complete A.T.L.A.S functionality with real-time market access.*"""

        return {
            "response": response,
            "type": "chat",
            "requires_action": False,
            "timestamp": datetime.now().isoformat()
        }

# Global A.T.L.A.S instance
atlas_brain = AtlasAIBrain()

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "status": "A.T.L.A.S AI Server Running",
        "version": "1.0.0",
        "system": "Advanced Trading & Learning Analysis System",
        "llm_integration": "OpenAI GPT-4" if atlas_brain.llm_enabled else "Simplified Mode",
        "mode": "Educational Paper Trading"
    }

@app.post("/api/v1/holly/chat", response_model=AtlasResponse)
async def chat_with_atlas(request: AtlasRequest):
    """
    Main A.T.L.A.S chat endpoint
    Advanced Trading & Learning Analysis System
    """
    try:
        result = await atlas_brain.process_user_message(
            user_message=request.message,
            user_context=request.user_context or {}
        )
        
        return AtlasResponse(
            response=result.get("response", "A.T.L.A.S is processing your request..."),
            type=result.get("type", "chat"),
            requires_action=result.get("requires_action", False),
            trading_plan=result.get("trading_plan"),
            plan_id=result.get("plan_id"),
            function_called=result.get("function_called"),
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        print(f"Error in A.T.L.A.S chat: {e}")
        raise HTTPException(status_code=500, detail=f"A.T.L.A.S encountered an error: {str(e)}")

@app.get("/api/v1/holly/capabilities")
async def get_capabilities():
    """Get enhanced A.T.L.A.S capabilities"""
    return {
        "name": "A.T.L.A.S",
        "full_name": "Advanced Trading & Learning Analysis System",
        "description": "Comprehensive AI trading assistant with real-time market access and full trading capabilities",
        "status": "Running with OpenAI GPT-4" if atlas_brain.llm_enabled else "Simplified Mode",
        "services_status": "Full Trading Services Enabled" if atlas_brain.services_enabled else "Limited Services",
        "api_integrations": {
            "alpaca_api": "Connected" if atlas_brain.services_enabled else "Not Available",
            "fmp_api": "Connected" if atlas_brain.services_enabled else "Not Available",
            "openai_api": "Connected" if atlas_brain.llm_enabled else "Not Configured",
            "web_search": "Available" if atlas_brain.services_enabled else "Not Available"
        },
        "core_capabilities": [
            "Real-time market data access via Alpaca & FMP APIs",
            "Comprehensive stock analysis (technical, fundamental, sentiment)",
            "AI-enhanced stop-loss calculations with support/resistance analysis",
            "Professional trading plan generation with bracket orders",
            "Live news sentiment analysis and market intelligence",
            "Multi-timeframe technical indicator analysis",
            "Position sizing and risk management optimization",
            "Market scanning and signal detection",
            "Educational explanations for all trading recommendations"
        ],
        "trading_features": [
            "Real-time quote access",
            "Comprehensive stock analysis",
            "AI-enhanced trading plans",
            "Smart stop-loss calculations",
            "Market news integration",
            "Risk management tools",
            "Portfolio optimization",
            "Bracket order support"
        ],
        "llm_model": "GPT-4 with Function Calling" if atlas_brain.llm_enabled else "Simplified Responses",
        "mode": "Professional Trading Assistant with Educational Focus",
        "personality": "ChatGPT for Trading - Conversational AI with Institutional Trading Expertise"
    }

# Mock endpoints for frontend compatibility
@app.get("/api/v1/account")
async def get_account():
    return {"portfolio_value": "100000.00", "buying_power": "50000.00", "daytrade_count": 0, "status": "ACTIVE"}

@app.get("/api/v1/positions")
async def get_positions():
    return []

@app.get("/api/v1/signals") 
async def get_signals():
    return []

if __name__ == "__main__":
    print("🚀 Starting A.T.L.A.S AI Server...")
    print("🎯 Advanced Trading & Learning Analysis System")
    print("📚 Educational Paper Trading Mode")
    
    uvicorn.run(
        "atlas_ai_server:app",
        host="0.0.0.0", 
        port=8080,
        reload=True,
        log_level="info"
    )
