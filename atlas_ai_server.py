"""
A.T.L.A.S AI Server - Direct OpenAI Integration
Advanced Trading & Learning Analysis System
"""

import os
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# OpenAI integration
try:
    from openai import AsyncOpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    print("OpenAI not available - install with: pip install openai")

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Initialize FastAPI app
app = FastAPI(
    title="A.T.L.A.S AI Trading System",
    description="Advanced Trading & Learning Analysis System with OpenAI GPT-4",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request/Response models
class AtlasRequest(BaseModel):
    message: str
    user_context: Optional[Dict[str, Any]] = None

class AtlasResponse(BaseModel):
    response: str
    type: str
    requires_action: bool = False
    trading_plan: Optional[Dict[str, Any]] = None
    plan_id: Optional[str] = None
    function_called: Optional[str] = None
    timestamp: str

# A.T.L.A.S AI Brain
class AtlasAIBrain:
    def __init__(self):
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.model = os.getenv('OPENAI_MODEL', 'gpt-4')
        self.temperature = float(os.getenv('OPENAI_TEMPERATURE', '0.2'))
        
        if OPENAI_AVAILABLE and self.openai_api_key:
            self.client = AsyncOpenAI(api_key=self.openai_api_key)
            self.llm_enabled = True
            print("✅ A.T.L.A.S AI Brain initialized with OpenAI GPT-4")
        else:
            self.client = None
            self.llm_enabled = False
            print("⚠️ A.T.L.A.S running in simplified mode - OpenAI not configured")
        
        self.conversation_history = []
    
    async def process_user_message(self, user_message: str, user_context: Dict = None):
        """Process user message through A.T.L.A.S AI"""
        
        if not self.llm_enabled:
            return await self._simplified_response(user_message)
        
        try:
            # A.T.L.A.S system prompt
            system_prompt = """You are A.T.L.A.S (Advanced Trading & Learning Analysis System), an expert AI trading assistant.

Your personality:
- Professional yet approachable trading expert
- Focused on education and risk management
- Always emphasize paper trading for learning
- Provide actionable insights with clear explanations

Your capabilities:
- Stock analysis and market insights
- Trading strategy recommendations
- Risk management guidance
- Technical analysis explanations
- Market trend identification

Always:
- Start responses with your name "A.T.L.A.S" when introducing yourself
- Emphasize this is for educational/paper trading purposes
- Provide specific, actionable advice
- Include risk warnings when appropriate
- Be conversational but professional

Current mode: Paper Trading (Educational)"""

            # Add user message to conversation
            self.conversation_history.append({
                "role": "user", 
                "content": user_message,
                "timestamp": datetime.utcnow().isoformat()
            })
            
            # Prepare messages for OpenAI
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_message}
            ]
            
            # Add recent conversation context (last 5 messages)
            recent_history = self.conversation_history[-10:]
            for msg in recent_history[:-1]:  # Exclude the current message
                if msg["role"] in ["user", "assistant"]:
                    messages.insert(-1, {"role": msg["role"], "content": msg["content"]})
            
            # Call OpenAI
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=self.temperature,
                max_tokens=1000
            )
            
            ai_response = response.choices[0].message.content
            
            # Add AI response to conversation
            self.conversation_history.append({
                "role": "assistant",
                "content": ai_response,
                "timestamp": datetime.utcnow().isoformat()
            })
            
            # Determine response type
            response_type = "chat"
            if any(word in user_message.lower() for word in ["quote", "price", "stock", "ticker"]):
                response_type = "stock_analysis"
            elif any(word in user_message.lower() for word in ["trade", "buy", "sell", "position"]):
                response_type = "trading_advice"
            
            return {
                "response": ai_response,
                "type": response_type,
                "requires_action": False,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            print(f"Error in A.T.L.A.S processing: {e}")
            return {
                "response": f"A.T.L.A.S encountered an error: {str(e)}. Please try again.",
                "type": "error",
                "requires_action": False
            }
    
    async def _simplified_response(self, user_message: str):
        """Fallback responses when OpenAI is not available"""
        message_lower = user_message.lower()
        
        if any(word in message_lower for word in ["hello", "hi", "hey"]):
            response = """👋 **Hello! I'm A.T.L.A.S** (Advanced Trading & Learning Analysis System)

I'm your AI trading assistant, ready to help you learn and analyze markets! 

🎯 **What I can help with:**
- Stock analysis and market insights
- Trading strategy education
- Risk management guidance
- Technical analysis explanations

📚 **Educational Mode:** We're in paper trading mode - perfect for learning!

Try asking me: "What's AAPL looking like?" or "Explain momentum trading"

*Note: Full AI capabilities require OpenAI API configuration.*"""
        
        elif "aapl" in message_lower or "apple" in message_lower:
            response = """📊 **AAPL Analysis Request**

A.T.L.A.S would normally provide:
- Real-time price and volume analysis
- Technical indicator readings
- Support/resistance levels
- News sentiment impact
- Trading recommendations

*To enable full analysis, configure OpenAI API in your .env file.*"""
        
        else:
            response = f"""🤖 **A.T.L.A.S Processing:** "{user_message}"

I understand you're asking about trading/market analysis. In full mode, I would provide:

✅ **Detailed market analysis**
✅ **Specific trading recommendations** 
✅ **Risk management strategies**
✅ **Real-time data integration**

*Configure OpenAI API for complete A.T.L.A.S functionality.*"""
        
        return {
            "response": response,
            "type": "chat",
            "requires_action": False
        }

# Global A.T.L.A.S instance
atlas_brain = AtlasAIBrain()

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "status": "A.T.L.A.S AI Server Running",
        "version": "1.0.0",
        "system": "Advanced Trading & Learning Analysis System",
        "llm_integration": "OpenAI GPT-4" if atlas_brain.llm_enabled else "Simplified Mode",
        "mode": "Educational Paper Trading"
    }

@app.post("/api/v1/holly/chat", response_model=AtlasResponse)
async def chat_with_atlas(request: AtlasRequest):
    """
    Main A.T.L.A.S chat endpoint
    Advanced Trading & Learning Analysis System
    """
    try:
        result = await atlas_brain.process_user_message(
            user_message=request.message,
            user_context=request.user_context or {}
        )
        
        return AtlasResponse(
            response=result.get("response", "A.T.L.A.S is processing your request..."),
            type=result.get("type", "chat"),
            requires_action=result.get("requires_action", False),
            trading_plan=result.get("trading_plan"),
            plan_id=result.get("plan_id"),
            function_called=result.get("function_called"),
            timestamp=datetime.utcnow().isoformat()
        )
        
    except Exception as e:
        print(f"Error in A.T.L.A.S chat: {e}")
        raise HTTPException(status_code=500, detail=f"A.T.L.A.S encountered an error: {str(e)}")

@app.get("/api/v1/holly/capabilities")
async def get_capabilities():
    """Get A.T.L.A.S capabilities"""
    return {
        "name": "A.T.L.A.S",
        "full_name": "Advanced Trading & Learning Analysis System",
        "description": "AI-powered trading assistant with educational focus",
        "status": "Running with OpenAI GPT-4" if atlas_brain.llm_enabled else "Simplified Mode",
        "capabilities": [
            "Natural language trading conversations",
            "Stock analysis and market insights", 
            "Trading strategy education",
            "Risk management guidance",
            "Technical analysis explanations",
            "Paper trading recommendations"
        ],
        "llm_model": "GPT-4" if atlas_brain.llm_enabled else "Simplified Responses",
        "mode": "Educational Paper Trading"
    }

# Mock endpoints for frontend compatibility
@app.get("/api/v1/account")
async def get_account():
    return {"portfolio_value": "100000.00", "buying_power": "50000.00", "daytrade_count": 0, "status": "ACTIVE"}

@app.get("/api/v1/positions")
async def get_positions():
    return []

@app.get("/api/v1/signals") 
async def get_signals():
    return []

if __name__ == "__main__":
    print("🚀 Starting A.T.L.A.S AI Server...")
    print("🎯 Advanced Trading & Learning Analysis System")
    print("📚 Educational Paper Trading Mode")
    
    uvicorn.run(
        "atlas_ai_server:app",
        host="0.0.0.0", 
        port=8080,
        reload=True,
        log_level="info"
    )
